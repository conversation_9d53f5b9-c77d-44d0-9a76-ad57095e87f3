import requests
import time
import logging
import os
import json
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class TPAApiService:
    """Service for TPA (Third Party Administrator) API integration"""

    # Configuration constants - moved to top for maintainability
    DEFAULT_TIMEOUT = 10 # seconds
    DEFAULT_BASE_URL = "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2"
    MAX_RETRIES = 1
    RETRY_DELAY = 3  # seconds
    DEFAULT_SSL_VERIFY = 'false'

    # API endpoints
    ENDPOINT_GET_TOKEN = "/api/GetToken"
    ENDPOINT_SEARCH_CITIZEN_ID = "/api/SearchCitizenID"
    ENDPOINT_CHECK_REGISTER = "/api/CheckRegister"
    ENDPOINT_POLICY_LIST_SOCIAL = "/api/PolicyListSocial"
    ENDPOINT_POLICY_DETAIL_SOCIAL = "/api/PolicyDetailSocial"

    # Content types
    CONTENT_TYPE_JSON = "application/json"
    CONTENT_TYPE_FORM_URLENCODED = "application/x-www-form-urlencoded"

    # HTTP methods
    HTTP_METHOD_POST = "POST"

    # Configuration file names
    CONFIG_FILES = [
        'policy-list-workflow.json',
        'policy-details-workflow.json'
    ]

    def __init__(self):
        self.session = requests.Session()
        # Set default timeout for all requests
        self.default_timeout = self.DEFAULT_TIMEOUT

        # Load configuration from workflow files
        self._load_configuration()

        # Configure SSL verification based on environment variables
        # For development/testing with expired certificates
        tpa_ssl_verify_env = self.DEFAULT_SSL_VERIFY
        self.verify_ssl = tpa_ssl_verify_env.lower() == 'true'

        # Apply SSL verification setting to the session
        self.session.verify = self.verify_ssl

        # Debug logging
        logger.info(f"TPA SSL Configuration - SSL verification enabled: {self.verify_ssl}")
        logger.info(f"TPA SSL Configuration - Session verify setting: {self.session.verify}")

        if not self.verify_ssl:
            logger.warning("TPA SSL verification is disabled. This should only be used in development/testing.")
            # Disable SSL warnings when verification is disabled
            try:
                import urllib3
                urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
                logger.info("SSL warnings disabled successfully")
            except ImportError:
                logger.warning("urllib3 not available, SSL warnings cannot be disabled")

    def _load_configuration(self):
        """Load configuration from workflow JSON files"""
        try:
            # Get the directory where this file is located
            current_dir = os.path.dirname(os.path.abspath(__file__))

            # Try to load from policy list workflow first, then policy details workflow as fallback
            config_loaded = False
            for config_file in self.CONFIG_FILES:
                try:
                    config_path = os.path.join(current_dir, config_file)
                    with open(config_path, 'r') as f:
                        workflow_config = json.load(f)

                    # Extract configuration from the first step (get_bearer_token)
                    token_step = workflow_config['steps'][0]
                    if token_step['name'] == 'get_bearer_token':
                        # Base URL is not in the workflow files, so we keep it hardcoded for now
                        # This could be moved to a separate config file in the future
                        self.base_url = self.DEFAULT_BASE_URL
                        self.username = token_step['request']['USERNAME']
                        self.password = token_step['request']['PASSWORD']

                        logger.info(f"TPA configuration loaded successfully from {config_file}")
                        config_loaded = True
                        break

                except (FileNotFoundError, KeyError, IndexError) as e:
                    logger.debug(f"Could not load configuration from {config_file}: {str(e)}")
                    continue

            if not config_loaded:
                raise Exception("No valid workflow configuration files found")

        except Exception as e:
            logger.error(f"Failed to load TPA configuration from workflow files: {str(e)}")

    def load_workflow_config(self, workflow_file_path: str) -> Dict[str, Any]:
        """Load configuration from a specific workflow JSON file"""
        try:
            with open(workflow_file_path, 'r') as f:
                workflow_config = json.load(f)

            # Update credentials if the workflow contains authentication step
            for step in workflow_config.get('steps', []):
                if step.get('name') == 'get_bearer_token' and 'request' in step:
                    request_data = step['request']
                    if 'USERNAME' in request_data and 'PASSWORD' in request_data:
                        self.username = request_data['USERNAME']
                        self.password = request_data['PASSWORD']
                        logger.info(f"Updated TPA credentials from workflow file: {workflow_file_path}")
                        break

            return workflow_config

        except Exception as e:
            logger.error(f"Failed to load workflow configuration from {workflow_file_path}: {str(e)}")
            raise Exception(f"Workflow configuration loading failed: {str(e)}")

    def _make_request_with_retry(self, method: str, url: str, **kwargs) -> requests.Response:
        """Make HTTP request with retry logic"""
        # Add default timeout if not specified
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.default_timeout

        # Debug logging
        logger.info(f"TPA API Request - Method: {method}, URL: {url}")
        logger.info(f"TPA API Request - Session verify: {self.session.verify}")
        logger.info(f"TPA API Request - Kwargs verify: {kwargs.get('verify', 'not set')}")

        last_exception = None
        for attempt in range(self.MAX_RETRIES):
            try:
                response = self.session.request(method, url, **kwargs)
                response.raise_for_status()
                logger.info(f"TPA API Request - Success on attempt {attempt + 1}")
                return response
            except requests.exceptions.RequestException as e:
                last_exception = e
                logger.warning(f"TPA API request failed (attempt {attempt + 1}/{self.MAX_RETRIES}): {str(e)}")
                if attempt < self.MAX_RETRIES - 1:
                    time.sleep(self.RETRY_DELAY * (2 ** attempt))  # Exponential backoff

        # If we get here, all retries failed
        logger.error(f"TPA API Request - All {self.MAX_RETRIES} attempts failed. Final error: {str(last_exception)}")
        raise last_exception or requests.exceptions.RequestException("All retry attempts failed")
    
    def get_bearer_token(self, social_id: str, channel_id: str, channel: str) -> str:
        """Get bearer token from TPA API"""
        url = f"{self.base_url}{self.ENDPOINT_GET_TOKEN}"

        payload = {
            "USERNAME": self.username,
            "PASSWORD": self.password,
            "SOCIAL_ID": social_id,
            "CHANNEL_ID": channel_id,
            "CHANNEL": channel
        }

        headers = {
            "Content-Type": self.CONTENT_TYPE_FORM_URLENCODED
        }

        try:
            response = self._make_request_with_retry(self.HTTP_METHOD_POST, url, data=payload, headers=headers)
            token_data = response.text
            return token_data
        except Exception as e:
            logger.error(f"Failed to get TPA bearer token: {str(e)}")
            raise Exception(f"TPA authentication failed: {str(e)}")
    
    def verify_citizen_id(self, token: str, citizen_id: str) -> Dict[str, Any]:
        """Verify citizen ID with TPA API"""
        url = f"{self.base_url}{self.ENDPOINT_SEARCH_CITIZEN_ID}"

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": self.CONTENT_TYPE_JSON
        }

        payload = {
            "CitizenID": citizen_id
        }

        try:
            response = self._make_request_with_retry(self.HTTP_METHOD_POST, url, json=payload, headers=headers)
            return response.json()
        except Exception as e:
            logger.error(f"Failed to verify citizen ID {citizen_id}: {str(e)}")
            raise Exception(f"Citizen ID verification failed: {str(e)}")
    
    def check_registration(self, token: str, citizen_id: str, social_id: str, channel_id: str) -> Dict[str, Any]:
        """Check registration status with TPA API"""
        url = f"{self.base_url}{self.ENDPOINT_CHECK_REGISTER}"

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": self.CONTENT_TYPE_JSON
        }

        payload = {
            "CitizenID": citizen_id,
            "SocialID": social_id,
            "ChannelID": channel_id
        }

        try:
            response = self._make_request_with_retry(self.HTTP_METHOD_POST, url, json=payload, headers=headers)
            return response.json()
        except Exception as e:
            logger.error(f"Failed to check registration for citizen {citizen_id}: {str(e)}")
            raise Exception(f"Registration check failed: {str(e)}")
    
    def get_policy_list(self, token: str, citizen_id: str, social_id: str, channel_id: str) -> Dict[str, Any]:
        """Get policy list from TPA API"""
        url = f"{self.base_url}{self.ENDPOINT_POLICY_LIST_SOCIAL}"

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": self.CONTENT_TYPE_JSON
        }

        payload = {
            "CitizenID": citizen_id,
            "SocialID": social_id,
            "ChannelID": channel_id
        }

        try:
            response = self._make_request_with_retry(self.HTTP_METHOD_POST, url, json=payload, headers=headers)
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get policy list for citizen {citizen_id}: {str(e)}")
            raise Exception(f"Policy list retrieval failed: {str(e)}")
    
    def get_policy_details(self, token: str, citizen_id: str, social_id: str, channel_id: str, member_code: str) -> Dict[str, Any]:
        """Get policy details from TPA API"""
        url = f"{self.base_url}{self.ENDPOINT_POLICY_DETAIL_SOCIAL}"

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": self.CONTENT_TYPE_JSON
        }

        payload = {
            "CitizenID": citizen_id,
            "SocialID": social_id,
            "ChannelID": channel_id,
            "MemberCode": member_code
        }

        try:
            response = self._make_request_with_retry(self.HTTP_METHOD_POST, url, json=payload, headers=headers)
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get policy details for member {member_code}: {str(e)}")
            raise Exception(f"Policy details retrieval failed: {str(e)}")

    # Dynamic request methods for JSON-driven workflows
    def make_dynamic_request(self, endpoint: str, method: str, payload: Dict[str, Any],
                           headers: Optional[Dict[str, str]] = None) -> Any:
        """Make a dynamic API request with configurable payload and headers"""
        url = f"{self.base_url}{endpoint}"

        # Default headers
        request_headers = {
            "Content-Type": self.CONTENT_TYPE_JSON
        }

        # Add custom headers if provided
        if headers:
            request_headers.update(headers)

        try:
            # Determine request method and payload format
            if method.upper() == self.HTTP_METHOD_POST:
                if request_headers.get("Content-Type") == self.CONTENT_TYPE_FORM_URLENCODED:
                    response = self._make_request_with_retry(method, url, data=payload, headers=request_headers)
                else:
                    response = self._make_request_with_retry(method, url, json=payload, headers=request_headers)
            else:
                response = self._make_request_with_retry(method, url, params=payload, headers=request_headers)

            # Handle different response types
            content_type = response.headers.get('content-type', '').lower()
            if self.CONTENT_TYPE_JSON in content_type:
                return response.json()
            else:
                # For token endpoint which returns plain text
                return response.text.strip().replace('"', '')

        except Exception as e:
            logger.error(f"Dynamic API request failed for {endpoint}: {str(e)}")
            raise Exception(f"API request to {endpoint} failed: {str(e)}")


