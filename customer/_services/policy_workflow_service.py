import uuid
import time
import logging
import json
import os
import re
from datetime import timed<PERSON><PERSON>
from typing import Dict, Any, Optional
from django.utils import timezone
from django.db import transaction
from django.contrib.auth.models import User

from customer.models import Customer, CustomerPlatformIdentity, CustomerPolicyWorkflowCache, CustomerPolicyWorkflowAuditLog
from .policy_tpa_service import TPAApiService
from customer.exceptions import PolicyWorkflowError, CustomerDataError

logger = logging.getLogger(__name__)

# Global configuration constants - moved to top for maintainability
WORKFLOW_CONFIG_FILES = {
    'POLICY_LIST': 'policy-list-workflow.json',
    'POLICY_DETAILS': 'policy-details-workflow.json'
}

# Workflow types
WORKFLOW_TYPE_POLICY_LIST = 'POLICY_LIST'
WORKFLOW_TYPE_POLICY_DETAILS = 'POLICY_DETAILS'

# Template pattern for variable resolution
TEMPLATE_VARIABLE_PATTERN = r'\{\{([^}]+)\}\}'

# Default retry settings
DEFAULT_RETRY_DELAY_SECONDS = 3
DEFAULT_MAX_RETRIES = 1

# Cache settings
DEFAULT_CACHE_DURATION_MINUTES = 1 # TODO: Change to 60-240 (1-4 hours) in production 

# Platform settings
DEFAULT_PLATFORM = 'LINE'

# Configuration modes
CONFIG_MODE_FIXED = 'fixed'
CONFIG_MODE_DATABASE = 'database'

# JSONPath constants
JSONPATH_ROOT = '$'
JSONPATH_PREFIX = '$.'
JSONPATH_ARRAY_WILDCARD = '[*]'

# Validation rules
VALIDATION_CITIZEN_ID_STATUS = "Status == '1'" 
VALIDATION_REGISTRATION_STATUS = "Status == 'YES'"

# Status values
STATUS_CITIZEN_ID_VALID = '1'
STATUS_REGISTRATION_YES = 'YES'

# Step handler names
STEP_GET_BEARER_TOKEN = 'get_bearer_token'
STEP_VERIFY_CITIZEN_ID = 'verify_citizen_id'
STEP_VERIFY_REGISTRATION = 'verify_registration'
STEP_FETCH_POLICY_LIST = 'fetch_policy_list'
STEP_FETCH_POLICY_DETAILS = 'fetch_policy_details'

# Response field names
FIELD_LIST_OF_SEARCH_CITIZEN_ID = 'ListOfSearchCitizenID'
FIELD_LIST_OF_CHECK_REGISTER = 'ListOfCheckRegister'
FIELD_LIST_OF_POLICY_LIST_SOCIAL = 'ListOfPolicyListSocial'
FIELD_LIST_OF_POL_DET = 'ListOfPolDet'
FIELD_LIST_OF_POL_CLAIM = 'ListOfPolClaim'
FIELD_STATUS = 'Status'
FIELD_MEMBER_CODE = 'MemberCode'

# Context keys
CONTEXT_STEP_DATA = 'step_data'
CONTEXT_STEP_RESULTS = 'step_results'
CONTEXT_TPA_CALLS = 'tpa_calls'
CONTEXT_TPA_TIME = 'tpa_time'
CONTEXT_CUSTOMER = 'customer'
CONTEXT_MEMBER_CODE = 'member_code'
CONTEXT_PROCESSED_DATA = 'processed_data'
CONTEXT_EXECUTION_ID = 'execution_id'

# Step result keys
RESULT_SUCCESS = 'success'
RESULT_ERROR = 'error'
RESULT_TIME_MS = 'time_ms'
RESULT_DATA = 'data'
RESULT_EXTRACTED_DATA = 'extracted_data'
RESULT_ATTEMPT = 'attempt'

# Configuration keys
CONFIG_KEY_CONFIG = 'config'
CONFIG_KEY_MODE = 'mode'
CONFIG_KEY_FIXED_VALUES = 'fixed_values'
CONFIG_KEY_STEPS = 'steps'
CONFIG_KEY_ID = 'id'
CONFIG_KEY_NAME = 'name'
CONFIG_KEY_RETRY = 'retry'
CONFIG_KEY_RETRY_DELAY_SECONDS = 'retry_delay_seconds'
CONFIG_KEY_EXTRACT = 'extract'
CONFIG_KEY_VALIDATE = 'validate'
CONFIG_KEY_REQUEST = 'request'
CONFIG_KEY_HEADERS = 'headers'
CONFIG_KEY_ENDPOINT = 'endpoint'
CONFIG_KEY_METHOD = 'method'

# Data keys for workflow results
DATA_KEY_POLICIES = 'policies'
DATA_KEY_POLICY_DETAILS = 'policy_details'
DATA_KEY_CLAIMS_DATA = 'claims_data'

# Error messages
ERROR_UNKNOWN_WORKFLOW_TYPE = "Unknown workflow type: {}"
ERROR_UNKNOWN_STEP_HANDLER = "Unknown step handler: {}"
ERROR_MAX_RETRIES_EXCEEDED = "Maximum retries exceeded"
ERROR_STEP_FAILED = "Step {} ({}) failed: {}"
ERROR_UNKNOWN_ERROR = "Unknown error"


class WorkflowConfigLoader:
    """Loads and manages workflow configurations from JSON files"""

    @staticmethod
    def load_workflow_config(workflow_type: str) -> Dict[str, Any]:
        """Load workflow configuration from JSON file"""

        if workflow_type not in WORKFLOW_CONFIG_FILES:
            raise PolicyWorkflowError(ERROR_UNKNOWN_WORKFLOW_TYPE.format(workflow_type))

        config_file = WORKFLOW_CONFIG_FILES[workflow_type]
        config_path = os.path.join(os.path.dirname(__file__), config_file)

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        except FileNotFoundError:
            raise PolicyWorkflowError(f"Workflow configuration file not found: {config_file}")
        except json.JSONDecodeError as e:
            raise PolicyWorkflowError(f"Invalid JSON in workflow configuration: {str(e)}")


class WorkflowTemplateResolver:
    """Resolves template variables in workflow configurations"""

    @staticmethod
    def resolve_template_variables(template: str, context: Dict[str, Any]) -> str:
        """Resolve template variables like {{variable_name}} in strings"""
        if not isinstance(template, str):
            return template

        # Find all template variables in the format {{variable_name}}
        matches = re.findall(TEMPLATE_VARIABLE_PATTERN, template)

        resolved = template
        for match in matches:
            variable_name = match.strip()
            if variable_name in context:
                resolved = resolved.replace(f'{{{{{variable_name}}}}}', str(context[variable_name]))
            else:
                logger.warning(f"Template variable '{variable_name}' not found in context")

        return resolved

    @staticmethod
    def resolve_request_data(request_data: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Resolve template variables in request data"""
        resolved = {}
        for key, value in request_data.items():
            if isinstance(value, str):
                resolved[key] = WorkflowTemplateResolver.resolve_template_variables(value, context)
            else:
                resolved[key] = value
        return resolved

    @staticmethod
    def resolve_headers(headers: Dict[str, str], context: Dict[str, Any]) -> Dict[str, str]:
        """Resolve template variables in headers"""
        resolved = {}
        for key, value in headers.items():
            resolved[key] = WorkflowTemplateResolver.resolve_template_variables(value, context)
        return resolved


class WorkflowDataExtractor:
    """Extracts data from API responses using JSONPath-like expressions"""

    @staticmethod
    def extract_data(response: Any, extract_config: Dict[str, str]) -> Dict[str, Any]:
        """Extract data from response using extraction configuration"""
        if not extract_config:
            return {}

        extracted = {}
        for key, path in extract_config.items():
            try:
                value = WorkflowDataExtractor._extract_by_path(response, path)
                extracted[key] = value
            except Exception as e:
                logger.warning(f"Failed to extract '{key}' using path '{path}': {str(e)}")
                extracted[key] = None

        return extracted

    @staticmethod
    def _extract_by_path(data: Any, path: str) -> Any:
        """Extract data using JSONPath-like syntax"""
        if path == JSONPATH_ROOT:
            return data

        if not path.startswith(JSONPATH_PREFIX):
            return None

        # Remove the '$.' prefix
        path_parts = path[2:].split('.')
        current = data

        for part in path_parts:
            if JSONPATH_ARRAY_WILDCARD in part:
                # Handle array extraction like 'ListOfPolicyListSocial[*].MemberCode'
                array_key = part.replace(JSONPATH_ARRAY_WILDCARD, '')
                if isinstance(current, dict) and array_key in current:
                    array_data = current[array_key]
                    if isinstance(array_data, list):
                        # Get the next part to extract from each array item
                        remaining_parts = path_parts[path_parts.index(part) + 1:]
                        if remaining_parts:
                            # Extract specific field from each array item
                            result = []
                            for item in array_data:
                                item_value = item
                                for remaining_part in remaining_parts:
                                    if isinstance(item_value, dict) and remaining_part in item_value:
                                        item_value = item_value[remaining_part]
                                    else:
                                        item_value = None
                                        break
                                if item_value is not None:
                                    result.append(item_value)
                            return result
                        else:
                            return array_data
                return None
            else:
                # Regular field access
                if isinstance(current, dict) and part in current:
                    current = current[part]
                else:
                    return None

        return current


class WorkflowValidator:
    """Validates workflow step results"""

    @staticmethod
    def validate_response(response: Any, validation_rule: str) -> bool:
        """Validate response against validation rule"""
        if not validation_rule:
            return True

        try:
            # Handle citizen ID verification
            if VALIDATION_CITIZEN_ID_STATUS in validation_rule:
                return WorkflowValidator._validate_citizen_id_status(response)

            # Handle registration verification
            elif VALIDATION_REGISTRATION_STATUS in validation_rule:
                return WorkflowValidator._validate_registration_status(response)

            else:
                logger.warning(f"Unknown validation rule: {validation_rule}")
                return True

        except Exception as e:
            logger.error(f"Validation failed: {str(e)}")
            return False

    @staticmethod
    def _validate_citizen_id_status(response: Any) -> bool:
        """Validate citizen ID verification response"""
        if not isinstance(response, dict):
            raise ValueError("Response must be a dictionary")

        search_results = response.get(FIELD_LIST_OF_SEARCH_CITIZEN_ID, [])
        if not isinstance(search_results, list) or len(search_results) == 0:
            raise ValueError("No citizen ID search results found")

        first_result = search_results[0]
        if not isinstance(first_result, dict):
            raise ValueError("Invalid search result format")

        status = first_result.get(FIELD_STATUS)
        if status != STATUS_CITIZEN_ID_VALID:
            raise ValueError(f"Citizen ID verification failed: Status = {status}")

        return True

    @staticmethod
    def _validate_registration_status(response: Any) -> bool:
        """Validate registration check response"""
        if not isinstance(response, dict):
            raise ValueError("Response must be a dictionary")

        check_results = response.get(FIELD_LIST_OF_CHECK_REGISTER, [])
        if not isinstance(check_results, list) or len(check_results) == 0:
            raise ValueError("No registration check results found")

        first_result = check_results[0]
        if not isinstance(first_result, dict):
            raise ValueError("Invalid check result format")

        status = first_result.get(FIELD_STATUS)
        if status != STATUS_REGISTRATION_YES:
            raise ValueError(f"Registration verification failed: Status = {status}")

        return True


class GenericWorkflowExecutor:
    """Generic workflow execution engine that processes JSON-defined workflows"""

    def __init__(self, tpa_service: TPAApiService):
        self.tpa_service = tpa_service
        self.step_handlers = {
            STEP_GET_BEARER_TOKEN: self._execute_get_bearer_token,
            STEP_VERIFY_CITIZEN_ID: self._execute_verify_citizen_id,
            STEP_VERIFY_REGISTRATION: self._execute_verify_registration,
            STEP_FETCH_POLICY_LIST: self._execute_fetch_policy_list,
            STEP_FETCH_POLICY_DETAILS: self._execute_fetch_policy_details,
        }

    def execute_workflow(self, workflow_type: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a complete workflow based on JSON configuration"""
        config = WorkflowConfigLoader.load_workflow_config(workflow_type)

        # Initialize execution context
        execution_context = {
            CONTEXT_STEP_DATA: {},
            CONTEXT_STEP_RESULTS: {},
            CONTEXT_TPA_CALLS: 0,
            CONTEXT_TPA_TIME: 0,
            **context
        }

        # Resolve data source (database or fixed values)
        self._resolve_data_source(config, execution_context)

        # Execute each step in sequence
        steps = sorted(config[CONFIG_KEY_STEPS], key=lambda x: x[CONFIG_KEY_ID])
        for step in steps:
            step_result = self._execute_step(step, execution_context)

            step_key = f"step_{step[CONFIG_KEY_ID]}_{step[CONFIG_KEY_NAME]}"
            execution_context[CONTEXT_STEP_RESULTS][step_key] = step_result

            if not step_result[RESULT_SUCCESS]:
                error_msg = ERROR_STEP_FAILED.format(
                    step[CONFIG_KEY_ID],
                    step[CONFIG_KEY_NAME],
                    step_result.get(RESULT_ERROR, ERROR_UNKNOWN_ERROR)
                )
                raise PolicyWorkflowError(error_msg)

            # Store extracted data for subsequent steps
            if RESULT_EXTRACTED_DATA in step_result:
                execution_context[CONTEXT_STEP_DATA].update(step_result[RESULT_EXTRACTED_DATA])

        return execution_context

    def _resolve_data_source(self, config: Dict[str, Any], context: Dict[str, Any]):
        """Resolve data source based on configuration mode"""
        config_mode = config.get(CONFIG_KEY_CONFIG, {}).get(CONFIG_KEY_MODE, CONFIG_MODE_FIXED)

        if config_mode == CONFIG_MODE_FIXED:
            # Use fixed values from configuration
            fixed_values = config.get(CONFIG_KEY_CONFIG, {}).get(CONFIG_KEY_FIXED_VALUES, {})
            context[CONTEXT_STEP_DATA].update(fixed_values)
        elif config_mode == CONFIG_MODE_DATABASE:
            # Use database queries from configuration
            database_config = config.get(CONFIG_KEY_CONFIG, {}).get('database', [])
            customer_id = context.get('customer_id') or context.get(CONTEXT_CUSTOMER).customer_id
            
            resolved_data = {}
            for db_query in database_config:
                table_data = self._execute_database_query(db_query, customer_id)
                resolved_data.update(table_data)
            
            context[CONTEXT_STEP_DATA].update(resolved_data)

        # Add member_code if provided in context
        if CONTEXT_MEMBER_CODE in context:
            context[CONTEXT_STEP_DATA][CONTEXT_MEMBER_CODE] = context[CONTEXT_MEMBER_CODE]

    def _execute_database_query(self, db_config: Dict[str, Any], customer_id: str) -> Dict[str, Any]:
        """Execute database query based on configuration"""
        from django.db import connection
        
        table = db_config['table']
        fields = db_config['fields']
        where_clause = db_config['where']
        
        # Replace named parameters with Django's placeholder syntax
        where_clause = where_clause.replace(':customer_id', '%s')
        
        # Build SQL query
        field_list = ', '.join(fields.values())
        sql = f"SELECT {field_list} FROM {table} WHERE {where_clause}"
        
        with connection.cursor() as cursor:
            cursor.execute(sql, [customer_id])
            row = cursor.fetchone()
            
            if row:
                # Map database fields to workflow variables
                result = {}
                for workflow_var, db_field in fields.items():
                    field_index = list(fields.values()).index(db_field)
                    result[workflow_var] = row[field_index]
                return result
        
        return {}

    def _execute_step(self, step: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a single workflow step with retry logic"""
        step_name = step[CONFIG_KEY_NAME]
        max_retries = step.get(CONFIG_KEY_RETRY, DEFAULT_MAX_RETRIES)

        for attempt in range(max_retries):
            try:
                start_time = time.time()

                # Get step handler
                handler = self.step_handlers.get(step_name)
                if not handler:
                    raise PolicyWorkflowError(ERROR_UNKNOWN_STEP_HANDLER.format(step_name))

                # Execute step
                response = handler(step, context)
                execution_time = (time.time() - start_time) * 1000

                # Extract data from response
                extracted_data = {}
                if CONFIG_KEY_EXTRACT in step:
                    extracted_data = WorkflowDataExtractor.extract_data(response, step[CONFIG_KEY_EXTRACT])

                # Validate response if validation rule exists
                if CONFIG_KEY_VALIDATE in step:
                    WorkflowValidator.validate_response(response, step[CONFIG_KEY_VALIDATE])

                # Update context counters
                context[CONTEXT_TPA_CALLS] += 1
                context[CONTEXT_TPA_TIME] += execution_time

                return {
                    RESULT_SUCCESS: True,
                    RESULT_TIME_MS: execution_time,
                    RESULT_DATA: response,
                    RESULT_EXTRACTED_DATA: extracted_data,
                    RESULT_ATTEMPT: attempt + 1
                }

            except Exception as e:
                logger.warning(f"Step {step_name} attempt {attempt + 1} failed: {str(e)}")
                if attempt == max_retries - 1:
                    # Last attempt failed
                    return {
                        RESULT_SUCCESS: False,
                        RESULT_ERROR: str(e),
                        RESULT_ATTEMPT: attempt + 1
                    }
                else:
                    # Wait before retry
                    retry_delay = step.get(CONFIG_KEY_RETRY_DELAY_SECONDS, DEFAULT_RETRY_DELAY_SECONDS)
                    time.sleep(retry_delay * (2 ** attempt))  # Exponential backoff

        # This should never be reached, but just in case
        return {
            RESULT_SUCCESS: False,
            RESULT_ERROR: ERROR_MAX_RETRIES_EXCEEDED,
            RESULT_ATTEMPT: max_retries
        }

    def _execute_get_bearer_token(self, step: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Execute get bearer token step"""
        request_data = WorkflowTemplateResolver.resolve_request_data(step[CONFIG_KEY_REQUEST], context[CONTEXT_STEP_DATA])
        headers = WorkflowTemplateResolver.resolve_headers(step.get(CONFIG_KEY_HEADERS, {}), context[CONTEXT_STEP_DATA])

        return self.tpa_service.make_dynamic_request(
            step[CONFIG_KEY_ENDPOINT],
            step[CONFIG_KEY_METHOD],
            request_data,
            headers
        )

    def _execute_verify_citizen_id(self, step: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Execute verify citizen ID step"""
        request_data = WorkflowTemplateResolver.resolve_request_data(step[CONFIG_KEY_REQUEST], context[CONTEXT_STEP_DATA])
        headers = WorkflowTemplateResolver.resolve_headers(step.get(CONFIG_KEY_HEADERS, {}), context[CONTEXT_STEP_DATA])

        return self.tpa_service.make_dynamic_request(
            step[CONFIG_KEY_ENDPOINT],
            step[CONFIG_KEY_METHOD],
            request_data,
            headers
        )

    def _execute_verify_registration(self, step: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Execute verify registration step"""
        request_data = WorkflowTemplateResolver.resolve_request_data(step[CONFIG_KEY_REQUEST], context[CONTEXT_STEP_DATA])
        headers = WorkflowTemplateResolver.resolve_headers(step.get(CONFIG_KEY_HEADERS, {}), context[CONTEXT_STEP_DATA])

        return self.tpa_service.make_dynamic_request(
            step[CONFIG_KEY_ENDPOINT],
            step[CONFIG_KEY_METHOD],
            request_data,
            headers
        )

    def _execute_fetch_policy_list(self, step: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Execute fetch policy list step"""
        request_data = WorkflowTemplateResolver.resolve_request_data(step[CONFIG_KEY_REQUEST], context[CONTEXT_STEP_DATA])
        headers = WorkflowTemplateResolver.resolve_headers(step.get(CONFIG_KEY_HEADERS, {}), context[CONTEXT_STEP_DATA])

        return self.tpa_service.make_dynamic_request(
            step[CONFIG_KEY_ENDPOINT],
            step[CONFIG_KEY_METHOD],
            request_data,
            headers
        )

    def _execute_fetch_policy_details(self, step: Dict[str, Any], context: Dict[str, Any]) -> Any:
        """Execute fetch policy details step"""
        request_data = WorkflowTemplateResolver.resolve_request_data(step[CONFIG_KEY_REQUEST], context[CONTEXT_STEP_DATA])
        headers = WorkflowTemplateResolver.resolve_headers(step.get(CONFIG_KEY_HEADERS, {}), context[CONTEXT_STEP_DATA])

        return self.tpa_service.make_dynamic_request(
            step[CONFIG_KEY_ENDPOINT],
            step[CONFIG_KEY_METHOD],
            request_data,
            headers
        )



class PolicyWorkflowService:
    """Service for executing policy workflows with caching and audit logging"""

    CACHE_DURATION_MINUTES = DEFAULT_CACHE_DURATION_MINUTES
    
    @classmethod
    def execute_policy_list_workflow(cls, customer_id: int, user: User) -> Dict[str, Any]:
        """Execute complete policy list workflow with caching"""
        execution_id = str(uuid.uuid4())
        start_time = time.time()
        
        try:
            # Get customer and platform identity
            customer = Customer.objects.get(customer_id=customer_id)
            platform_identity = cls._get_platform_identity(customer)
            
            # Check cache first
            cached_result = cls._get_cached_result(customer, WORKFLOW_TYPE_POLICY_LIST)
            if cached_result:
                logger.info(f"Returning cached policy list for customer {customer_id}")
                return cached_result[CONTEXT_PROCESSED_DATA]

            # Execute workflow using generic engine
            tpa_service = TPAApiService()
            workflow_executor = GenericWorkflowExecutor(tpa_service)

            context = {
                CONTEXT_CUSTOMER: customer,
                'platform_identity': platform_identity,
                CONTEXT_EXECUTION_ID: execution_id
            }

            execution_result = workflow_executor.execute_workflow(WORKFLOW_TYPE_POLICY_LIST, context)

            # Extract workflow data
            policy_list_data = execution_result[CONTEXT_STEP_DATA].get(DATA_KEY_POLICIES, [])
            raw_response = {FIELD_LIST_OF_POLICY_LIST_SOCIAL: policy_list_data}

            # Process and format data
            processed_data = cls._process_policy_list_data(raw_response)

            # Cache the result
            total_time = (time.time() - start_time) * 1000
            cls._cache_result(customer, WORKFLOW_TYPE_POLICY_LIST, None, platform_identity,
                            raw_response, processed_data, execution_id, total_time)

            # Log audit
            cls._log_audit(customer, user, WORKFLOW_TYPE_POLICY_LIST, execution_id, execution_result[CONTEXT_STEP_RESULTS],
                          total_time, True, None, execution_result[CONTEXT_TPA_CALLS], execution_result[CONTEXT_TPA_TIME])

            return processed_data
            
        except Exception as e:
            total_time = (time.time() - start_time) * 1000
            error_details = {'error': str(e), 'type': type(e).__name__}

            # Log failed audit
            step_results = {}
            tpa_calls = 0
            tpa_time = 0
            cls._log_audit(customer, user, WORKFLOW_TYPE_POLICY_LIST, execution_id, step_results,
                          total_time, False, error_details, tpa_calls, tpa_time)

            logger.error(f"Policy list workflow failed for customer {customer_id}: {str(e)}")
            raise PolicyWorkflowError(f"Policy list workflow failed: {str(e)}")
    
    @classmethod
    def execute_policy_details_workflow(cls, customer_id: int, member_code: str, user: User) -> Dict[str, Any]:
        """Execute complete policy details workflow with caching"""
        execution_id = str(uuid.uuid4())
        start_time = time.time()

        try:
            # Get customer and platform identity
            customer = Customer.objects.get(customer_id=customer_id)
            platform_identity = cls._get_platform_identity(customer)

            # Check cache first
            cached_result = cls._get_cached_result(customer, WORKFLOW_TYPE_POLICY_DETAILS, member_code)
            if cached_result:
                logger.info(f"Returning cached policy details for customer {customer_id}, member {member_code}")
                return cached_result[CONTEXT_PROCESSED_DATA]

            # Execute workflow using generic engine
            tpa_service = TPAApiService()
            workflow_executor = GenericWorkflowExecutor(tpa_service)

            context = {
                CONTEXT_CUSTOMER: customer,
                'platform_identity': platform_identity,
                CONTEXT_EXECUTION_ID: execution_id,
                CONTEXT_MEMBER_CODE: member_code
            }

            execution_result = workflow_executor.execute_workflow(WORKFLOW_TYPE_POLICY_DETAILS, context)

            # Extract workflow data
            policy_details = execution_result[CONTEXT_STEP_DATA].get(DATA_KEY_POLICY_DETAILS, [])
            claims_data = execution_result[CONTEXT_STEP_DATA].get(DATA_KEY_CLAIMS_DATA, [])
            raw_response = {
                FIELD_LIST_OF_POL_DET: policy_details,
                FIELD_LIST_OF_POL_CLAIM: claims_data
            }

            # Process and format data
            processed_data = cls._process_policy_details_data(raw_response, member_code)

            # Cache the result
            total_time = (time.time() - start_time) * 1000
            cls._cache_result(customer, WORKFLOW_TYPE_POLICY_DETAILS, member_code, platform_identity,
                            raw_response, processed_data, execution_id, total_time)

            # Log audit
            cls._log_audit(customer, user, WORKFLOW_TYPE_POLICY_DETAILS, execution_id, execution_result[CONTEXT_STEP_RESULTS],
                          total_time, True, None, execution_result[CONTEXT_TPA_CALLS], execution_result[CONTEXT_TPA_TIME])

            return processed_data

        except Exception as e:
            total_time = (time.time() - start_time) * 1000
            error_details = {'error': str(e), 'type': type(e).__name__}

            # Log failed audit
            step_results = {}
            tpa_calls = 0
            tpa_time = 0
            cls._log_audit(customer, user, WORKFLOW_TYPE_POLICY_DETAILS, execution_id, step_results,
                          total_time, False, error_details, tpa_calls, tpa_time)

            logger.error(f"Policy details workflow failed for customer {customer_id}, member {member_code}: {str(e)}")
            raise PolicyWorkflowError(f"Policy details workflow failed: {str(e)}")
    
    @classmethod
    def _get_platform_identity(cls, customer: Customer) -> CustomerPlatformIdentity:
        """Get platform identity for customer"""
        platform_identity = customer.get_identity_for_platform(DEFAULT_PLATFORM)
        if not platform_identity:
            raise CustomerDataError(f"No {DEFAULT_PLATFORM} platform identity found for customer {customer.customer_id}")
        return platform_identity

    @classmethod
    def _get_cached_result(cls, customer: Customer, workflow_type: str, member_code: Optional[str] = None) -> Optional[Dict]:
        """Get cached workflow result if available and not expired"""
        try:
            cache_entry = CustomerPolicyWorkflowCache.objects.get(
                customer=customer,
                workflow_type=workflow_type,
                member_code=member_code,
                expires_at__gt=timezone.now()
            )
            return {
                CONTEXT_PROCESSED_DATA: cache_entry.processed_data,
                CONTEXT_EXECUTION_ID: cache_entry.execution_id
            }
        except CustomerPolicyWorkflowCache.DoesNotExist:
            return None

    @classmethod
    def _cache_result(cls, customer: Customer, workflow_type: str, member_code: Optional[str],
                     platform_identity: CustomerPlatformIdentity, raw_data: Dict, processed_data: Dict,
                     execution_id: str, execution_time: float):
        """Cache workflow result"""
        expires_at = timezone.now() + timedelta(minutes=cls.CACHE_DURATION_MINUTES)

        with transaction.atomic():
            # Delete existing cache entry if exists
            CustomerPolicyWorkflowCache.objects.filter(
                customer=customer,
                workflow_type=workflow_type,
                member_code=member_code
            ).delete()

            # Create new cache entry
            CustomerPolicyWorkflowCache.objects.create(
                customer=customer,
                workflow_type=workflow_type,
                member_code=member_code,
                citizen_id=customer.national_id,
                social_id=platform_identity.platform_user_id,
                channel_id=platform_identity.channel_id,
                raw_response_data=raw_data,
                processed_data=processed_data,
                execution_id=execution_id,
                execution_time_ms=int(execution_time),
                success=True,
                expires_at=expires_at
            )

    @classmethod
    def _log_audit(cls, customer: Customer, user: User, workflow_type: str, execution_id: str,
                  step_results: Dict, total_time: float, success: bool, error_details: Optional[Dict],
                  tpa_calls: int, tpa_time: float):
        """Log workflow execution audit"""
        CustomerPolicyWorkflowAuditLog.objects.create(
            customer=customer,
            requested_by=user,
            workflow_type=workflow_type,
            execution_id=execution_id,
            step_results=step_results,
            total_execution_time_ms=int(total_time),
            success=success,
            error_details=error_details,
            tpa_calls_made=tpa_calls,
            tpa_total_time_ms=int(tpa_time)
        )

    @classmethod
    def _process_policy_list_data(cls, raw_data: Dict) -> Dict[str, Any]:
        """Process and format policy list data for frontend"""
        policy_list = raw_data.get(FIELD_LIST_OF_POLICY_LIST_SOCIAL, [])

        # Extract member codes
        member_codes = []
        for policy in policy_list:
            member_code = policy.get(FIELD_MEMBER_CODE)
            if member_code and member_code not in member_codes:
                member_codes.append(member_code)

        return {
            'policy_list_data': raw_data,
            'member_codes': member_codes,
            'execution_metadata': {
                'total_policies': len(policy_list),
                'unique_member_codes': len(member_codes),
                'processed_at': timezone.now().isoformat()
            }
        }

    @classmethod
    def _process_policy_details_data(cls, raw_data: Dict, member_code: str) -> Dict[str, Any]:
        """Process and format policy details data for frontend"""
        policy_details = raw_data.get(FIELD_LIST_OF_POL_DET, [])
        policy_claims = raw_data.get(FIELD_LIST_OF_POL_CLAIM, [])

        return {
            'policy_details_data': raw_data,
            CONTEXT_MEMBER_CODE: member_code,
            'execution_metadata': {
                'total_policy_details': len(policy_details),
                'total_claims': len(policy_claims),
                'processed_at': timezone.now().isoformat()
            }
        }
