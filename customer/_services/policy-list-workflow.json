{"name": "Policy List API Workflow", "version": "1.0.0", "config": {"mode": "database", "fixed_values": {"social_id": "U3ef2199803607a9ec643f2461fd2f039", "channel_id": "2006769099", "citizen_id": "2019086318637", "channel": "LINE"}, "database": [{"table": "customer_customerplatformidentity", "fields": {"social_id": "platform_user_id", "channel_id": "channel_id", "channel": "platform"}, "where": "customer_id = :customer_id"}, {"table": "customer_customer", "fields": {"citizen_id": "national_id"}, "where": "customer_id = :customer_id"}]}, "steps": [{"id": 1, "name": "get_bearer_token", "endpoint": "/api/GetToken", "method": "POST", "request": {"USERNAME": "BVTPA", "PASSWORD": "*d!n^+Cb@1", "SOCIAL_ID": "{{social_id}}", "CHANNEL_ID": "{{channel_id}}", "CHANNEL": "{{channel}}"}, "extract": {"bearer_token": "$"}, "retry": 3}, {"id": 2, "name": "verify_citizen_id", "endpoint": "/api/SearchCitizenID", "method": "POST", "headers": {"Authorization": "Bearer {{bearer_token}}"}, "request": {"SOCIAL_ID": "{{social_id}}", "CHANNEL_ID": "{{channel_id}}", "CHANNEL": "{{channel}}"}, "validate": "$.ListOfSearchCitizenID[0].Status == '1'", "retry": 2}, {"id": 3, "name": "verify_registration", "endpoint": "/api/CheckRegister", "method": "POST", "headers": {"Authorization": "Bearer {{bearer_token}}"}, "request": {"CITIZEN_ID": "{{citizen_id}}", "SOCIAL_ID": "{{social_id}}", "CHANNEL_ID": "{{channel_id}}", "CHANNEL": "{{channel}}", "CONTACT": ""}, "validate": "$.ListOfCheckRegister[0].Status == 'YES'", "retry": 2}, {"id": 4, "name": "fetch_policy_list", "endpoint": "/api/PolicyListSocial", "method": "POST", "headers": {"Authorization": "Bearer {{bearer_token}}"}, "request": {"CITIZEN_ID": "{{citizen_id}}", "SOCIAL_ID": "{{social_id}}", "CHANNEL_ID": "{{channel_id}}", "CHANNEL": "{{channel}}"}, "extract": {"policies": "$.ListOfPolicyListSocial", "member_codes": "$.ListOfPolicyListSocial[*].MemberCode"}, "retry": 2}], "storage": {"table": "CustomerPolicyList", "key": ["customer_id"], "data": {"customer_id": "{{input.customer_id}}", "citizen_id": "{{citizen_id}}", "policies_data": "{{policies}}", "member_codes": "{{member_codes}}", "last_updated": "{{timestamp}}"}}, "options": {"timeout_minutes": 5, "retry_delay_seconds": 3}}